// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

// Previous generator and datasource blocks remain the same
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Admin {
  id          String   @id @default(cuid())
  email       String   @unique
  password    String
  name        String
  phoneNumber String?
  designation String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  isVerified Boolean   @default(false)
  otp        String?
  otpExpiry  DateTime?
  isActive   Boolean   @default(true)

  refreshTokens RefreshToken[]
}

model Teacher {
  id             String     @id @default(cuid())
  email          String     @unique
  password       String
  name           String
  phoneNumber    String?
  qualification  String
  specialization String?
  department     Department @relation(fields: [departmentId], references: [id])
  departmentId   String
  courses        Course[]
  resources      Resource[]
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt

  isVerified Boolean   @default(false)
  otp        String?
  otpExpiry  DateTime?
  isBanned   Boolean   @default(false)

  // Communication
  conversations     Conversation[]
  resourceComments  ResourceComment[]
  newsEventComments NewsEventComment[]
  refreshTokens     RefreshToken[]
}

model Student {
  id           String       @id @default(cuid())
  email        String       @unique
  password     String
  name         String
  phoneNumber  String?
  enrollmentNo String       @unique
  department   Department   @relation(fields: [departmentId], references: [id])
  departmentId String
  enrollments  Enrollment[]
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  isVerified Boolean   @default(false)
  otp        String?
  otpExpiry  DateTime?
  isBanned   Boolean   @default(false)

  // Communication
  conversations     Conversation[]
  resourceComments  ResourceComment[]
  newsEventComments NewsEventComment[]
  refreshTokens     RefreshToken[]
}

model Department {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?
  courses     Course[]
  teachers    Teacher[]
  students    Student[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model Course {
  id           String       @id @default(cuid())
  name         String
  code         String       @unique
  description  String?
  duration     String
  capacity     Int          @default(30)
  department   Department   @relation(fields: [departmentId], references: [id])
  departmentId String
  teacher      Teacher      @relation(fields: [teacherId], references: [id])
  teacherId    String
  enrollments  Enrollment[]
  resources    Resource[]
  syllabus     String? // URL or file path
  startDate    DateTime?
  endDate      DateTime?
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
}

model Enrollment {
  id         String           @id @default(cuid())
  student    Student          @relation(fields: [studentId], references: [id])
  studentId  String
  course     Course           @relation(fields: [courseId], references: [id])
  courseId   String
  status     EnrollmentStatus @default(PENDING)
  enrolledAt DateTime         @default(now())
  updatedAt  DateTime         @updatedAt

  @@unique([studentId, courseId])
}

enum EnrollmentStatus {
  PENDING
  APPROVED
  REJECTED
  WITHDRAWN
}

model Resource {
  id          String            @id @default(cuid())
  title       String
  description String?
  type        ResourceType
  url         String
  mimeType    String?
  course      Course            @relation(fields: [courseId], references: [id])
  courseId    String
  teacher     Teacher           @relation(fields: [teacherId], references: [id])
  teacherId   String
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  comments    ResourceComment[]
  isPublic    Boolean           @default(false)
}

enum ResourceType {
  DOCUMENT
  VIDEO
  LINK
  ASSIGNMENT
  QUIZ
}

model NewsEvent {
  id          String             @id @default(cuid())
  title       String
  content     String
  type        NewsEventType
  date        DateTime
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  comments    NewsEventComment[]
  isPublished Boolean            @default(false)
}

enum NewsEventType {
  NEWS
  EVENT
  ANNOUNCEMENT
}

// Communication Models
model Conversation {
  id        String   @id @default(cuid())
  teacherId String
  studentId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  teacher  Teacher   @relation(fields: [teacherId], references: [id])
  student  Student   @relation(fields: [studentId], references: [id])
  messages Message[]

  @@unique([teacherId, studentId])
}

model Message {
  id             String   @id @default(cuid())
  conversationId String
  content        String
  isFromTeacher  Boolean
  createdAt      DateTime @default(now())

  // Read tracking
  teacherReadAt DateTime?
  studentReadAt DateTime?

  conversation Conversation @relation(fields: [conversationId], references: [id])
}

model ResourceComment {
  id         String   @id @default(cuid())
  content    String
  resource   Resource @relation(fields: [resourceId], references: [id])
  resourceId String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Comment can be made by either student or teacher
  teacher   Teacher? @relation(fields: [teacherId], references: [id])
  teacherId String?
  student   Student? @relation(fields: [studentId], references: [id])
  studentId String?

  parentComment ResourceComment?  @relation("CommentReplies", fields: [parentId], references: [id])
  parentId      String?
  replies       ResourceComment[] @relation("CommentReplies")
}

model NewsEventComment {
  id          String    @id @default(cuid())
  content     String
  newsEvent   NewsEvent @relation(fields: [newsEventId], references: [id])
  newsEventId String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Comment can be made by either student or teacher
  teacher   Teacher? @relation(fields: [teacherId], references: [id])
  teacherId String?
  student   Student? @relation(fields: [studentId], references: [id])
  studentId String?

  parentComment NewsEventComment?  @relation("CommentReplies", fields: [parentId], references: [id])
  parentId      String?
  replies       NewsEventComment[] @relation("CommentReplies")
}

model RefreshToken {
  id        String   @id @default(uuid())
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations - one of these will be set
  adminId   String?
  teacherId String?
  studentId String?

  // Foreign keys
  admin   Admin?   @relation(fields: [adminId], references: [id], onDelete: Cascade)
  teacher Teacher? @relation(fields: [teacherId], references: [id], onDelete: Cascade)
  student Student? @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@index([adminId])
  @@index([teacherId])
  @@index([studentId])
  @@index([token])
}
