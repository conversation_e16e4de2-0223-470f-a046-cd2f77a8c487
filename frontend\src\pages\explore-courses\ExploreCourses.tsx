import { useState, useEffect } from "react";
import {
  Typography,
  Card,
  Row,
  Col,
  Input,
  Select,
  Pagination,
  Spin,
  Empty,
  Tag,
  Button,
  Space,
  Divider,
} from "antd";
import {
  SearchOutlined,
  CalendarOutlined,
  UserOutlined,
  BookOutlined,
  TeamOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import { courseService, PublicCoursesParams } from "@/services/courseService";
import { departmentService } from "@/services/departmentService";
import { useApi } from "@/hooks";

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;
const { Option } = Select;

interface Course {
  id: string;
  name: string;
  code: string;
  description: string;
  duration: number;
  capacity: number;
  startDate: string;
  endDate: string;
  createdAt: string;
  department: {
    id: string;
    name: string;
    description: string;
  };
  teacher: {
    id: string;
    name: string;
    email: string;
    qualification?: string;
    specialization?: string;
  };
  _count: {
    enrollments: number;
    resources: number;
  };
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalCourses: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  limit: number;
}

const ExploreCourses = () => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    currentPage: 1,
    totalPages: 1,
    totalCourses: 0,
    hasNextPage: false,
    hasPrevPage: false,
    limit: 9,
  });
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<PublicCoursesParams>({
    page: 1,
    limit: 9,
    search: "",
    departmentId: "",
  });

  // Fetch departments for filter
  const { data: departmentsData } = useApi(
    () => departmentService.getDepartmentsForSelect(),
    { immediate: true }
  );

  // Fetch courses
  const fetchCourses = async (params: PublicCoursesParams) => {
    setLoading(true);
    try {
      const response = await courseService.getPublicCourses(params);
      if (response.data) {
        setCourses(response.data);
        setPagination(response.pagination);
      }
    } catch (error) {
      console.error("Error fetching courses:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCourses(searchParams);
  }, [searchParams]);

  const handleSearch = (value: string) => {
    setSearchParams((prev) => ({
      ...prev,
      search: value,
      page: 1,
    }));
  };

  const handleDepartmentFilter = (departmentId: string) => {
    setSearchParams((prev) => ({
      ...prev,
      departmentId,
      page: 1,
    }));
  };

  const handlePageChange = (page: number) => {
    setSearchParams((prev) => ({
      ...prev,
      page,
    }));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <Title level={1} className="mb-4">
            Explore Our Courses
          </Title>
          <Paragraph className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover our comprehensive range of vocational training courses
            designed to equip you with practical skills for today's job market.
          </Paragraph>
        </div>

        {/* Filters */}
        <Card className="mb-8 shadow-sm">
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={12} md={8}>
              <Search
                placeholder="Search courses..."
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                onSearch={handleSearch}
                onChange={(e) => {
                  if (!e.target.value) {
                    handleSearch("");
                  }
                }}
              />
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Select
                placeholder="Filter by Department"
                allowClear
                size="large"
                style={{ width: "100%" }}
                onChange={handleDepartmentFilter}
                value={searchParams.departmentId || undefined}
              >
                {departmentsData?.data?.map((dept: any) => (
                  <Option key={dept.value} value={dept.value}>
                    {dept.label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col xs={24} md={8}>
              <Text type="secondary">
                Showing {courses.length} of {pagination.totalCourses} courses
              </Text>
            </Col>
          </Row>
        </Card>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <Spin size="large" />
          </div>
        )}

        {/* Empty State */}
        {!loading && courses.length === 0 && (
          <Empty
            description="No courses found"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}

        {/* Courses Grid */}
        {!loading && courses.length > 0 && (
          <>
            <Row gutter={[24, 24]} className="mb-8">
              {courses.map((course) => (
                <Col xs={24} sm={12} lg={8} key={course.id}>
                  <Card
                    hoverable
                    className="h-full shadow-md hover:shadow-lg transition-shadow"
                    cover={
                      <div className="h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                        <BookOutlined className="text-6xl text-white" />
                      </div>
                    }
                    actions={[
                      <Button type="primary" href="/register">
                        Enroll Now
                      </Button>,
                    ]}
                  >
                    <div className="h-full flex flex-col">
                      <div className="flex-1">
                        <div className="mb-3">
                          <Tag color="blue">{course.code}</Tag>
                          <Tag color="green">{course.department.name}</Tag>
                        </div>

                        <Title level={4} className="mb-2 line-clamp-2">
                          {course.name}
                        </Title>

                        <Paragraph
                          className="text-gray-600 mb-4 line-clamp-3"
                          ellipsis={{ rows: 3 }}
                        >
                          {course.description}
                        </Paragraph>

                        <Divider className="my-3" />

                        {/* Course Info */}
                        <Space
                          direction="vertical"
                          size="small"
                          className="w-full"
                        >
                          <div className="flex items-center justify-between">
                            <Space>
                              <UserOutlined className="text-gray-500" />
                              <Text strong>{course.teacher.name}</Text>
                            </Space>
                          </div>

                          {course.teacher.qualification && (
                            <Text type="secondary" className="text-sm">
                              {course.teacher.qualification}
                            </Text>
                          )}

                          {course.teacher.specialization && (
                            <Text type="secondary" className="text-sm">
                              Specialization: {course.teacher.specialization}
                            </Text>
                          )}

                          <div className="flex items-center justify-between mt-2">
                            <Space>
                              <ClockCircleOutlined className="text-gray-500" />
                              <Text>{course.duration} months</Text>
                            </Space>
                            <Space>
                              <TeamOutlined className="text-gray-500" />
                              <Text>
                                {course._count.enrollments}/{course.capacity}
                              </Text>
                            </Space>
                          </div>

                          <div className="flex items-center justify-between">
                            <Space>
                              <CalendarOutlined className="text-gray-500" />
                              <Text className="text-sm">
                                {formatDate(course.startDate)} -{" "}
                                {formatDate(course.endDate)}
                              </Text>
                            </Space>
                          </div>
                        </Space>
                      </div>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>

            {/* Pagination */}
            <div className="text-center">
              <Pagination
                current={pagination.currentPage}
                total={pagination.totalCourses}
                pageSize={pagination.limit}
                onChange={handlePageChange}
                showSizeChanger={false}
                showQuickJumper
                showTotal={(total, range) =>
                  `${range[0]}-${range[1]} of ${total} courses`
                }
              />
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default ExploreCourses;
