// Message Input Styles - WhatsApp-like
.message-input {
  .input-container {
    display: flex;
    align-items: flex-end;
    gap: 8px;
    background: white;
    border-radius: 24px;
    padding: 8px 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .emoji-btn {
      color: #667781;
      border: none;
      background: transparent;
      padding: 8px;
      height: auto;
      min-width: auto;
      
      &:hover {
        background: #f5f5f5;
        color: #25d366;
      }
      
      &:focus {
        background: #f5f5f5;
        color: #25d366;
      }
    }

    .message-textarea {
      flex: 1;
      border: none;
      box-shadow: none;
      resize: none;
      font-size: 14px;
      line-height: 1.4;
      padding: 8px 0;
      background: transparent;

      &:focus {
        border: none;
        box-shadow: none;
        outline: none;
      }

      &::placeholder {
        color: #667781;
      }

      // Remove Ant Design's default textarea styling
      .ant-input {
        border: none;
        box-shadow: none;
        padding: 0;
        background: transparent;

        &:focus {
          border: none;
          box-shadow: none;
        }
      }
    }

    .send-btn {
      background: #25d366;
      border-color: #25d366;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      flex-shrink: 0;

      &:hover {
        background: #20b858;
        border-color: #20b858;
      }

      &:focus {
        background: #20b858;
        border-color: #20b858;
      }

      &:disabled {
        background: #ccc;
        border-color: #ccc;
        cursor: not-allowed;
      }

      .anticon {
        font-size: 16px;
      }
    }
  }

  .input-footer {
    margin-top: 8px;
    text-align: center;

    .input-hint {
      font-size: 11px;
      color: #667781;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .message-input {
    .input-container {
      padding: 6px 10px;
      
      .send-btn {
        width: 36px;
        height: 36px;
        
        .anticon {
          font-size: 14px;
        }
      }
    }

    .input-footer {
      .input-hint {
        font-size: 10px;
      }
    }
  }
}

// Dark mode support (future enhancement)
@media (prefers-color-scheme: dark) {
  .message-input {
    .input-container {
      background: #2a2f32;
      
      .message-textarea {
        color: #e9edef;
        
        &::placeholder {
          color: #8696a0;
        }
      }
      
      .emoji-btn {
        color: #8696a0;
        
        &:hover {
          background: #3b4a54;
          color: #25d366;
        }
      }
    }
    
    .input-footer {
      .input-hint {
        color: #8696a0;
      }
    }
  }
}
