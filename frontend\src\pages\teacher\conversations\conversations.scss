// Teacher Conversations - WhatsApp-like styling
.teacher-conversations {
  height: calc(100vh - (var(--header-height) + 20px));
  background: #f0f2f5;
  overflow: hidden;

  .conversations-layout {
    height: 100%;
    background: transparent;
    overflow: hidden;

    .conversations-sidebar {
      background: white;
      border-right: 1px solid #e0e0e0;
      height: 100%;
      overflow: hidden;

      .sidebar-header {
        padding: 16px;
        border-bottom: 1px solid #e0e0e0;
        background: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .header-content {
          display: flex;
          align-items: center;

          .header-icon {
            font-size: 20px;
            color: #25d366;
            margin-right: 8px;
          }

          .header-title {
            margin: 0;
            color: #111b21;
            font-weight: 600;
          }
        }

        .new-conversation-btn {
          background: #25d366;
          border-color: #25d366;
          border-radius: 20px;
          height: 36px;
          font-size: 12px;

          &:hover {
            background: #20b858;
            border-color: #20b858;
          }
        }
      }
    }

    .chat-content {
      background: #efeae2;
      height: 90%;
      position: relative;
      overflow: hidden;
    }
  }
}

// Conversation List Styles
.conversation-list {
  height: calc(100% - 73px);
  overflow-y: auto;
  overflow-x: hidden;

  .conversation-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;

    &:hover {
      background: #f5f5f5;
    }

    &.selected {
      background: #e7f3ff;
    }

    .conversation-avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: #25d366;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
      font-size: 18px;
      margin-right: 12px;
      flex-shrink: 0;
    }

    .conversation-content {
      flex: 1;
      min-width: 0;

      .conversation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;

        .participant-name {
          font-weight: 600;
          color: #111b21;
          font-size: 16px;
          margin: 0;
        }

        .conversation-time {
          font-size: 12px;
          color: #667781;
        }
      }

      .conversation-preview {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .last-message {
          color: #667781;
          font-size: 14px;
          margin: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex: 1;
        }

        .unread-badge {
          background: #25d366;
          color: white;
          border-radius: 10px;
          padding: 2px 6px;
          font-size: 12px;
          font-weight: 600;
          min-width: 20px;
          text-align: center;
          margin-left: 8px;
        }
      }
    }
  }

  .load-more-btn {
    width: 100%;
    margin: 16px 0;
    border: none;
    background: transparent;
    color: #25d366;

    &:hover {
      background: #f5f5f5;
    }
  }
}

// Chat Area Styles
.chat-area {
  height: 100%;
  display: flex;
  flex-direction: column;

  .chat-header {
    background: #f0f2f5;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;

    .participant-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #25d366;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
      margin-right: 12px;
    }

    .participant-info {
      .participant-name {
        font-weight: 600;
        color: #111b21;
        margin: 0;
        font-size: 16px;
      }

      .participant-status {
        color: #667781;
        font-size: 13px;
        margin: 0;
      }
    }
  }

  .messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxwYXR0ZXJuIGlkPSJhIiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIiB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgcGF0dGVyblRyYW5zZm9ybT0icm90YXRlKDEyKSI+CiAgICAgIDxwYXRoIGQ9Im0wIDBoMzAwdjMwMGgtMzAweiIgZmlsbD0iI2VmZWFlMiIvPgogICAgICA8cGF0aCBkPSJtMCAwaDMwMHYzMDBoLTMwMHoiIGZpbGw9InVybCgjYikiLz4KICAgIDwvcGF0dGVybj4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNhKSIvPgo8L3N2Zz4=');
  }

  .message-input-container {
    background: #f0f2f5;
    padding: 16px 20px;
    border-top: 1px solid #e0e0e0;
  }
}

// Empty state
.empty-chat {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #667781;

  .empty-icon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .empty-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
  }

  .empty-description {
    font-size: 14px;
    text-align: center;
    max-width: 300px;
  }
}
